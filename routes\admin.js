const express = require('express');
const router = express.Router();
const Category = require('../models/Category');
const Link = require('../models/Link');
const SiteSetting = require('../models/SiteSetting');
const AdminPassword = require('../models/AdminPassword');
const axios = require('axios');
const cheerio = require('cheerio');

// 初始化密码文件
AdminPassword.initializePassword();

// 管理员配置（生产环境中应该存储在数据库或环境变量中）
const ADMIN_CONFIG = {
    username: 'admin',
    password: 'admin123' // 生产环境中应该使用加密密码
};

// 身份验证中间件
function requireAuth(req, res, next) {
    if (req.session && req.session.isAuthenticated) {
        return next();
    } else {
        return res.status(401).json({ message: '未授权访问' });
    }
}

// 登录页面
router.get('/login', (req, res) => {
    res.sendFile('admin/login.html', { root: './public' });
});

// 登录处理
router.post('/login', (req, res) => {
    const { username, password, rememberMe } = req.body;

    if (username === ADMIN_CONFIG.username && AdminPassword.validateCurrentPassword(password)) {
        req.session.isAuthenticated = true;
        req.session.username = username;

        // 如果选择记住我，延长session时间
        if (rememberMe) {
            req.session.cookie.maxAge = 30 * 24 * 60 * 60 * 1000; // 30天
        }

        res.json({
            success: true,
            message: '登录成功',
            user: { username: username }
        });
    } else {
        res.status(401).json({
            success: false,
            message: '用户名或密码错误'
        });
    }
});

// 登出处理
router.post('/logout', (req, res) => {
    req.session.destroy((err) => {
        if (err) {
            return res.status(500).json({ message: '登出失败' });
        }
        res.json({ message: '登出成功' });
    });
});

// 检查认证状态
router.get('/check-auth', (req, res) => {
    if (req.session && req.session.isAuthenticated) {
        res.json({
            authenticated: true,
            user: { username: req.session.username }
        });
    } else {
        res.status(401).json({ authenticated: false });
    }
});

// 管理页面（需要认证）
router.get('/', requireAuth, (req, res) => {
    res.sendFile('admin/index.html', { root: './public' });
});

// 图表数据API
router.get('/stats/chart', requireAuth, (req, res) => {
    const days = parseInt(req.query.days) || 7;

    // 生成模拟图表数据
    const labels = [];
    const visits = [];
    const clicks = [];
    const ips = [];

    const today = new Date();

    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);

        // 格式化日期
        const dateStr = date.toISOString().split('T')[0];
        const monthDay = `${date.getMonth() + 1}月${date.getDate()}日`;
        labels.push(monthDay);

        // 生成模拟数据（基于日期生成相对稳定的随机数）
        const seed = date.getTime() / (1000 * 60 * 60 * 24);
        const visitCount = Math.floor(Math.sin(seed * 0.1) * 3 + Math.cos(seed * 0.2) * 2 + 5 + Math.random() * 2);
        const clickCount = Math.floor(Math.sin(seed * 0.15) * 2 + Math.cos(seed * 0.25) * 1.5 + 3 + Math.random() * 1.5);
        const ipCount = Math.floor(Math.sin(seed * 0.08) * 1.5 + Math.cos(seed * 0.18) * 1 + 2 + Math.random() * 1);

        visits.push(Math.max(0, visitCount));
        clicks.push(Math.max(0, clickCount));
        ips.push(Math.max(0, ipCount));
    }

    res.json({
        labels,
        visits,
        clicks,
        ips
    });
});

// 分类API
// 获取所有分类
router.get('/categories', requireAuth, async (req, res) => {
    try {
        const categories = await Category.find().sort({ order: 1 });
        res.json(categories);
    } catch (err) {
        // 如果数据库操作失败，返回模拟数据
        console.log('数据库操作失败，返回模拟数据:', err.message);
        res.json([
            { _id: '1', name: '搜索引擎', order: 1 },
            { _id: '2', name: '开发工具', order: 2 },
            { _id: '3', name: '技术论坛', order: 3 },
            { _id: '4', name: '在线AI', order: 4 }
        ]);
    }
});

// 添加分类
router.post('/categories', requireAuth, async (req, res) => {
    const category = new Category({
        name: req.body.name,
        order: req.body.order || 0
    });
    
    try {
        const newCategory = await category.save();
        res.status(201).json(newCategory);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
});

// 更新分类
router.put('/categories/:id', requireAuth, async (req, res) => {
    try {
        const category = await Category.findById(req.params.id);
        if (!category) return res.status(404).json({ message: '分类不存在' });
        
        if (req.body.name) category.name = req.body.name;
        if (req.body.order !== undefined) category.order = req.body.order;
        
        const updatedCategory = await category.save();
        res.json(updatedCategory);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
});

// 删除分类
router.delete('/categories/:id', requireAuth, async (req, res) => {
    try {
        const category = await Category.findById(req.params.id);
        if (!category) return res.status(404).json({ message: '分类不存在' });
        
        await category.deleteOne();
        // 删除该分类下的所有链接
        await Link.deleteMany({ categoryId: req.params.id });
        
        res.json({ message: '分类已删除' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});

// 链接API
// 获取所有链接
router.get('/links', requireAuth, async (req, res) => {
    try {
        const links = await Link.find();
        res.json(links);
    } catch (err) {
        // 如果数据库操作失败，返回模拟数据
        console.log('数据库操作失败，返回模拟数据:', err.message);
        res.json([
            { _id: '1', title: 'Google', url: 'https://www.google.com', description: '全球最大的搜索引擎', customIcon: null, categoryId: '1', clicks: 0 },
            { _id: '2', title: '百度', url: 'https://www.baidu.com', description: '中国最大的搜索引擎', customIcon: null, categoryId: '1', clicks: 0 },
            { _id: '3', title: 'GitHub', url: 'https://github.com', description: '代码托管平台', customIcon: null, categoryId: '2', clicks: 0 },
            { _id: '4', title: 'Stack Overflow', url: 'https://stackoverflow.com', description: '程序员问答社区', customIcon: null, categoryId: '2', clicks: 0 },
            { _id: '5', title: '掘金', url: 'https://juejin.cn', description: '技术社区', customIcon: null, categoryId: '3', clicks: 0 },
            { _id: '6', title: 'ChatGPT', url: 'https://chat.openai.com', description: 'OpenAI的对话AI', customIcon: 'https://cdn.openai.com/API/logo-openai.svg', categoryId: '4', clicks: 0 }
        ]);
    }
});

// 获取单个链接
router.get('/links/:id', requireAuth, async (req, res) => {
    console.log('获取单个链接请求:', req.params.id);

    try {
        const link = await Link.findById(req.params.id);
        if (!link) {
            console.log('数据库中未找到链接，尝试模拟数据');
            throw new Error('链接不存在');
        }
        console.log('从数据库获取链接成功:', link);
        res.json(link);
    } catch (err) {
        // 如果数据库操作失败，返回模拟数据
        console.log('数据库操作失败，返回模拟数据:', err.message);
        const mockLinks = [
            { _id: '1', title: 'Google', url: 'https://www.google.com', description: '全球最大的搜索引擎', customIcon: null, categoryId: '1', clicks: 0 },
            { _id: '2', title: '百度', url: 'https://www.baidu.com', description: '中国最大的搜索引擎', customIcon: null, categoryId: '1', clicks: 0 },
            { _id: '3', title: 'GitHub', url: 'https://github.com', description: '代码托管平台', customIcon: null, categoryId: '2', clicks: 0 },
            { _id: '4', title: 'Stack Overflow', url: 'https://stackoverflow.com', description: '程序员问答社区', customIcon: null, categoryId: '2', clicks: 0 },
            { _id: '5', title: '掘金', url: 'https://juejin.cn', description: '技术社区', customIcon: null, categoryId: '3', clicks: 0 },
            { _id: '6', title: 'ChatGPT', url: 'https://chat.openai.com', description: 'OpenAI的对话AI', customIcon: 'https://cdn.openai.com/API/logo-openai.svg', categoryId: '4', clicks: 0 }
        ];

        // 尝试通过ID匹配，如果是MongoDB ObjectId格式，则使用第一个作为示例
        let link = mockLinks.find(l => l._id === req.params.id);

        if (!link && req.params.id.length === 24) {
            // 如果是24位的ObjectId格式，返回第一个模拟数据作为示例
            link = { ...mockLinks[0], _id: req.params.id };
            console.log('使用模拟数据，ID:', req.params.id);
        }

        if (link) {
            console.log('返回模拟链接数据:', link);
            res.json(link);
        } else {
            console.log('未找到匹配的链接');
            res.status(404).json({ message: '链接不存在' });
        }
    }
});

// 添加链接
router.post('/links', requireAuth, async (req, res) => {
    const link = new Link({
        title: req.body.title,
        url: req.body.url,
        description: req.body.description,
        customIcon: req.body.customIcon,
        categoryId: req.body.categoryId
    });

    try {
        const newLink = await link.save();
        res.status(201).json(newLink);
    } catch (err) {
        res.status(400).json({ message: err.message });
    }
});

// 更新链接
router.put('/links/:id', requireAuth, async (req, res) => {
    console.log('更新链接请求:', req.params.id, req.body);

    try {
        const link = await Link.findById(req.params.id);
        if (!link) {
            console.log('数据库中未找到链接，使用模拟更新');
            throw new Error('链接不存在');
        }

        if (req.body.title) link.title = req.body.title;
        if (req.body.url) link.url = req.body.url;
        if (req.body.description !== undefined) link.description = req.body.description;
        if (req.body.customIcon !== undefined) link.customIcon = req.body.customIcon;
        if (req.body.categoryId) link.categoryId = req.body.categoryId;

        const updatedLink = await link.save();
        console.log('数据库更新成功:', updatedLink);
        res.json(updatedLink);
    } catch (err) {
        console.log('数据库更新失败，返回模拟成功响应:', err.message);
        // 在模拟模式下，返回更新后的数据
        const updatedLink = {
            _id: req.params.id,
            title: req.body.title || 'Google',
            url: req.body.url || 'https://www.google.com',
            description: req.body.description || '全球最大的搜索引擎',
            customIcon: req.body.customIcon || null,
            categoryId: req.body.categoryId || '1',
            clicks: 0
        };
        console.log('返回模拟更新结果:', updatedLink);
        res.json(updatedLink);
    }
});

// 删除链接
router.delete('/links/:id', requireAuth, async (req, res) => {
    try {
        const link = await Link.findById(req.params.id);
        if (!link) return res.status(404).json({ message: '链接不存在' });
        
        await link.deleteOne();
        res.json({ message: '链接已删除' });
    } catch (err) {
        res.status(500).json({ message: err.message });
    }
});

// 获取网站信息API
router.post('/fetch-site-info', requireAuth, async (req, res) => {
    const { url } = req.body;

    if (!url) {
        return res.status(400).json({ message: '请提供URL' });
    }

    try {
        console.log('正在获取网站信息:', url);

        // 确保URL格式正确
        let targetUrl = url;
        if (!targetUrl.startsWith('http://') && !targetUrl.startsWith('https://')) {
            targetUrl = 'https://' + targetUrl;
        }

        // 设置请求头，模拟浏览器访问
        const response = await axios.get(targetUrl, {
            timeout: 10000, // 10秒超时
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            },
            maxRedirects: 5
        });

        const $ = cheerio.load(response.data);

        // 获取标题
        let title = $('title').text().trim();

        // 如果没有title标签，尝试其他方式
        if (!title) {
            title = $('meta[property="og:title"]').attr('content') ||
                   $('meta[name="twitter:title"]').attr('content') ||
                   $('h1').first().text().trim() ||
                   '未知标题';
        }

        // 获取描述
        let description = $('meta[name="description"]').attr('content') ||
                         $('meta[property="og:description"]').attr('content') ||
                         $('meta[name="twitter:description"]').attr('content') ||
                         '';

        // 如果没有meta描述，尝试获取第一段文字
        if (!description) {
            const firstP = $('p').first().text().trim();
            if (firstP && firstP.length > 10) {
                description = firstP.length > 200 ? firstP.substring(0, 200) + '...' : firstP;
            }
        }

        // 清理标题和描述
        title = title.replace(/\s+/g, ' ').trim();
        description = description.replace(/\s+/g, ' ').trim();

        // 限制长度
        if (title.length > 100) {
            title = title.substring(0, 100) + '...';
        }
        if (description.length > 300) {
            description = description.substring(0, 300) + '...';
        }

        console.log('获取到的网站信息:', { title, description });

        res.json({
            title: title || '未知标题',
            description: description || ''
        });

    } catch (error) {
        console.error('获取网站信息失败:', error.message);

        // 根据错误类型返回不同的错误信息
        let errorMessage = '获取网站信息失败';

        if (error.code === 'ENOTFOUND') {
            errorMessage = '无法访问该网站，请检查URL是否正确';
        } else if (error.code === 'ECONNREFUSED') {
            errorMessage = '网站拒绝连接';
        } else if (error.code === 'ETIMEDOUT') {
            errorMessage = '请求超时，网站响应过慢';
        } else if (error.response && error.response.status) {
            errorMessage = `网站返回错误: ${error.response.status}`;
        }

        res.status(500).json({
            message: errorMessage,
            details: error.message
        });
    }
});

// 站点设置API
// 获取站点设置
router.get('/site-settings', requireAuth, async (req, res) => {
    try {
        const settings = await SiteSetting.getSetting();
        res.json(settings);
    } catch (err) {
        console.log('数据库操作失败，返回默认设置:', err.message);
        // 返回默认设置
        res.json({
            siteName: '我的导航网站',
            logo: '',
            icpNumber: '',
            policeNumber: '',
            startTime: new Date('2024-01-01'),
            updatedAt: new Date()
        });
    }
});

// 更新站点设置（不包含运行时间）
router.put('/site-settings', requireAuth, async (req, res) => {
    try {
        const { siteName, logo, icpNumber, policeNumber, announcement } = req.body;

        const updateData = {};
        if (siteName !== undefined) updateData.siteName = siteName;
        if (logo !== undefined) updateData.logo = logo;
        if (icpNumber !== undefined) updateData.icpNumber = icpNumber;
        if (policeNumber !== undefined) updateData.policeNumber = policeNumber;
        if (announcement !== undefined) updateData.announcement = announcement;
        // 注意：不更新startTime

        const settings = await SiteSetting.updateSetting(updateData);
        res.json(settings);
    } catch (err) {
        console.log('数据库操作失败，返回模拟成功:', err.message);
        // 返回模拟成功响应
        res.json({
            siteName: req.body.siteName || '我的导航网站',
            logo: req.body.logo || '',
            icpNumber: req.body.icpNumber || '',
            policeNumber: req.body.policeNumber || '',
            announcement: req.body.announcement || {
                enabled: false,
                title: '',
                content: '',
                countdown: 10,
                showOnce: true,
                createdAt: new Date()
            },
            // 保持原有的startTime，不修改
            startTime: new Date('2024-01-01'),
            updatedAt: new Date()
        });
    }
});

// 单独更新运行时间
router.put('/site-settings/runtime', requireAuth, async (req, res) => {
    try {
        const { startTime } = req.body;

        if (!startTime) {
            return res.status(400).json({ message: '请提供运行开始时间' });
        }

        const updateData = {
            startTime: new Date(startTime)
        };

        const settings = await SiteSetting.updateSetting(updateData);
        res.json(settings);
    } catch (err) {
        console.log('数据库操作失败，返回模拟成功:', err.message);
        // 返回模拟成功响应
        res.json({
            siteName: '我的导航网站',
            logo: '',
            icpNumber: '',
            policeNumber: '',
            startTime: req.body.startTime || new Date('2024-01-01'),
            updatedAt: new Date()
        });
    }
});

// 修改密码API
router.post('/change-password', requireAuth, async (req, res) => {
    const { currentPassword, newPassword } = req.body;

    if (!currentPassword || !newPassword) {
        return res.status(400).json({ message: '请提供当前密码和新密码' });
    }

    if (newPassword.length < 6) {
        return res.status(400).json({ message: '新密码长度至少6位' });
    }

    try {
        // 使用密码管理模块修改密码
        const result = AdminPassword.changePassword(currentPassword, newPassword);

        console.log('密码修改成功:', {
            oldPassword: '***',
            newPassword: '***',
            timestamp: new Date().toISOString()
        });

        // 返回成功响应
        res.json(result);

    } catch (error) {
        console.error('修改密码失败:', error.message);
        res.status(400).json({ message: error.message });
    }
});

// 公告查看记录API
const announcementViews = new Map(); // 存储IP查看记录

// 检查IP是否已查看公告
router.get('/announcement/check/:ip', async (req, res) => {
    try {
        const ip = req.params.ip;
        const settings = await SiteSetting.getSetting();

        if (!settings.announcement || !settings.announcement.enabled) {
            return res.json({ shouldShow: false });
        }

        // 如果设置为每次都显示，直接返回true
        if (!settings.announcement.showOnce) {
            return res.json({
                shouldShow: true,
                announcement: settings.announcement
            });
        }

        // 检查IP是否已查看过
        const viewKey = `${ip}_${settings.announcement.createdAt}`;
        const hasViewed = announcementViews.has(viewKey);

        res.json({
            shouldShow: !hasViewed,
            announcement: hasViewed ? null : settings.announcement
        });
    } catch (err) {
        console.log('检查公告查看记录失败:', err.message);
        res.json({ shouldShow: false });
    }
});

// 记录IP已查看公告
router.post('/announcement/viewed', async (req, res) => {
    try {
        const { ip } = req.body;
        const settings = await SiteSetting.getSetting();

        if (settings.announcement && settings.announcement.enabled && settings.announcement.showOnce) {
            const viewKey = `${ip}_${settings.announcement.createdAt}`;
            announcementViews.set(viewKey, true);
        }

        res.json({ success: true });
    } catch (err) {
        console.log('记录公告查看失败:', err.message);
        res.json({ success: false });
    }
});

module.exports = router;



