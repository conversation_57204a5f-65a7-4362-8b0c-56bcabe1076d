<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>导航页管理后台</title>
    <!-- 引入Chart.js图表库 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #e8e8e8;
        }
        .admin-container {
            display: flex;
            min-height: 100vh;
            width: 1400px;
            margin: 0 auto;
            background-color: #f5f5f5;
        }
        .sidebar {
            width: 250px;
            background-color: #2c3e50;
            color: white;
            padding: 20px;
        }
        .sidebar h2 {
            margin-top: 0;
            padding-bottom: 10px;
            border-bottom: 1px solid #34495e;
        }
        .sidebar ul {
            list-style: none;
            padding: 0;
        }
        .sidebar li {
            margin-bottom: 10px;
        }
        .sidebar a {
            color: white;
            text-decoration: none;
            display: block;
            padding: 10px;
            border-radius: 5px;
        }
        .sidebar a:hover {
            background-color: #34495e;
        }
        .content {
            flex: 1;
            padding: 20px;
            background-color: #f5f5f5;
            box-sizing: border-box;
        }
        .section {
            display: block;
        }
        .hidden {
            display: none;
        }
        .card {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #2980b9;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f2f2f2;
        }

        /* 固定宽度表单样式 */
        .fixed-width-form {
            width: 600px;
        }

        .fixed-width-form .form-group {
            margin-bottom: 15px;
        }

        .fixed-width-form input,
        .fixed-width-form select,
        .fixed-width-form textarea {
            width: 500px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }

        .fixed-width-form button {
            width: 200px;
        }

        /* 统计卡片样式 */
        .stats-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
            width: 800px;
            margin: 0 auto;
        }

        .stats-card {
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
            flex: 1;
            min-width: 150px;
        }

        .stats-card h3 {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 16px;
        }

        .stats-card p {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            color: #333;
        }

        /* 图表容器样式 */
        .chart-container {
            width: 100%;
        }

        /* 搜索控件容器 */
        .search-controls {
            display: flex;
            gap: 10px;
            align-items: center;
            justify-content: flex-end;
        }

        /* 小屏幕适配 */
        @media (max-width: 1450px) {
            .admin-container {
                width: 100%;
                margin: 0;
            }

            .fixed-width-form {
                width: 100%;
                max-width: 600px;
            }

            .fixed-width-form input,
            .fixed-width-form select,
            .fixed-width-form textarea {
                width: 100%;
                max-width: 500px;
            }

            .fixed-width-form button {
                width: auto;
                max-width: 200px;
            }

            .stats-container {
                width: 100%;
                max-width: 800px;
            }
        }

        @media (max-width: 768px) {
            .content {
                padding: 15px;
            }

            .stats-container {
                flex-direction: column;
                gap: 15px;
            }

            .search-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 10px;
            }

            .card {
                padding: 15px;
            }
        }

        /* 弹窗样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 8px;
            width: 600px;
            max-width: 90%;
            max-height: 80%;
            overflow-y: auto;
            position: relative;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }

        .modal-header h2 {
            margin: 0;
            color: #333;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #000;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid #ddd;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
        }

        .btn-secondary {
            background-color: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background-color: #7f8c8d;
        }

        /* 分类管理按钮样式 */
        .move-btn {
            padding: 5px 8px;
            margin-right: 5px;
            background-color: #f39c12;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            font-weight: bold;
        }

        .move-btn:hover:not(:disabled) {
            background-color: #e67e22;
        }

        .move-btn:disabled {
            background-color: #bdc3c7;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .edit-btn {
            padding: 5px 10px;
            margin-right: 5px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .edit-btn:hover {
            background-color: #2980b9;
        }

        .delete-btn {
            padding: 5px 10px;
            background-color: #e74c3c;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }

        .delete-btn:hover {
            background-color: #c0392b;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <nav class="sidebar">
            <h2>管理菜单</h2>
            <div style="margin-bottom: 20px; padding: 10px; background-color: #34495e; border-radius: 5px;">
                <p style="margin: 0; color: #ecf0f1; font-size: 14px;">
                    欢迎，<span id="username">管理员</span>
                </p>
            </div>
            <ul>
                <li><a href="#dashboard" class="nav-link" data-section="dashboard">仪表盘</a></li>
                <li><a href="#categories" class="nav-link" data-section="categories">分类管理</a></li>
                <li><a href="#links" class="nav-link" data-section="links">链接管理</a></li>
                <li><a href="#settings" class="nav-link" data-section="settings">站点设置</a></li>
                <li><a href="#" onclick="logout()" style="color: #e74c3c;">登出</a></li>
            </ul>
        </nav>
        <main class="content">
            <div id="dashboard" class="section">
                <h1>仪表盘</h1>

                <div class="card">
                    <h2>管理员设置</h2>
                    <form id="password-form" class="fixed-width-form">
                        <div class="form-group">
                            <label for="current-password">当前密码</label>
                            <input type="password" id="current-password" required placeholder="请输入当前密码">
                        </div>
                        <div class="form-group">
                            <label for="new-password">新密码</label>
                            <input type="password" id="new-password" required placeholder="请输入新密码" minlength="6">
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                密码长度至少6位
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="confirm-password">确认新密码</label>
                            <input type="password" id="confirm-password" required placeholder="请再次输入新密码">
                        </div>
                        <button type="submit">修改密码</button>
                    </form>
                </div>

                <div class="card">
                    <h2>网站统计</h2>
                    <div id="stats-container">
                        <p>加载中...</p>
                    </div>
                </div>

                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h2>📊 报表统计</h2>
                        <select id="chart-period" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 120px; font-size: 14px;">
                            <option value="7">最近7天</option>
                            <option value="14">最近14天</option>
                            <option value="30">最近30天</option>
                        </select>
                    </div>
                    <div class="chart-container" style="position: relative; height: 400px;">
                        <canvas id="statsChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div id="categories" class="section hidden">
                <h1>分类管理</h1>
                <div class="card">
                    <h2>添加分类</h2>
                    <form id="category-form" class="fixed-width-form">
                        <div class="form-group">
                            <label for="category-name">分类名称</label>
                            <input type="text" id="category-name" required>
                        </div>
                        <div class="form-group">
                            <label for="category-order">排序顺序</label>
                            <input type="number" id="category-order" value="0">
                        </div>
                        <button type="submit">添加分类</button>
                    </form>
                </div>
                
                <div class="card">
                    <h2>分类列表</h2>
                    <table id="categories-table">
                        <thead>
                            <tr>
                                <th>名称</th>
                                <th>排序</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 分类列表将通过JS动态生成 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div id="links" class="section hidden">
                <h1>链接管理</h1>
                <div class="card">
                    <h2>添加链接</h2>
                    <form id="link-form" class="fixed-width-form">
                        <div class="form-group">
                            <label for="link-title">标题</label>
                            <input type="text" id="link-title" required>
                        </div>
                        <div class="form-group">
                            <label for="link-url">URL</label>
                            <div style="display: flex; gap: 10px; align-items: center;">
                                <input type="url" id="link-url" required style="flex: 1;">
                                <button type="button" id="fetch-info-btn" onclick="fetchSiteInfo()" style="padding: 8px 15px; background-color: #27ae60; color: white; border: none; border-radius: 4px; cursor: pointer; white-space: nowrap;">
                                    🔍 识别
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="link-description">描述</label>
                            <textarea id="link-description" rows="3"></textarea>
                        </div>
                        <div class="form-group">
                            <label for="link-icon">自定义图标URL（可选）</label>
                            <input type="url" id="link-icon" placeholder="https://example.com/icon.png">
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                留空则使用网站默认图标，支持 .png、.jpg、.ico、.svg 格式
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="link-category">分类</label>
                            <select id="link-category" required>
                                <!-- 分类选项将通过JS动态生成 -->
                            </select>
                        </div>
                        <button type="submit">添加链接</button>
                    </form>
                </div>
                
                <div class="card">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; flex-wrap: wrap; gap: 15px;">
                        <h2>链接列表</h2>
                        <div class="search-controls">
                            <select id="category-filter" style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 150px; font-size: 14px;">
                                <option value="">所有分类</option>
                                <!-- 分类选项将通过JS动态生成 -->
                            </select>
                            <input
                                type="text"
                                id="search-input"
                                placeholder="搜索标题或链接..."
                                style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 200px; font-size: 14px;"
                            >
                            <button
                                onclick="clearSearch()"
                                style="padding: 8px 12px; background-color: #95a5a6; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;"
                            >
                                清除
                            </button>
                        </div>
                    </div>
                    <table id="links-table">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>URL</th>
                                <th>分类</th>
                                <th>点击量</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 链接列表将通过JS动态生成 -->
                        </tbody>
                    </table>
                    <div id="search-results-info" style="margin-top: 10px; color: #666; font-size: 14px; display: none;">
                        <!-- 搜索结果信息 -->
                    </div>
                </div>
            </div>

            <div id="settings" class="section hidden">
                <h1>站点设置</h1>
                <div class="card">
                    <h2>基本设置</h2>
                    <form id="settings-form" class="fixed-width-form">
                        <div class="form-group">
                            <label for="site-name">网站名称</label>
                            <input type="text" id="site-name" required placeholder="我的导航网站">
                        </div>
                        <div class="form-group">
                            <label for="site-logo">网站Logo URL</label>
                            <input type="url" id="site-logo" placeholder="https://example.com/logo.png">
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                留空则显示网站名称，支持 .png、.jpg、.svg 格式
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="icp-number">ICP备案号</label>
                            <input type="text" id="icp-number" placeholder="京ICP备12345678号">
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                留空则不显示备案信息
                            </small>
                        </div>
                        <div class="form-group">
                            <label for="police-number">公安备案号</label>
                            <input type="text" id="police-number" placeholder="京公网安备11010802012345号">
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                留空则不显示公安备案信息
                            </small>
                        </div>
                        <button type="submit">保存基本设置</button>
                    </form>
                </div>

                <div class="card">
                    <h2>运行时间设置</h2>
                    <form id="runtime-form" class="fixed-width-form">
                        <div class="form-group">
                            <label for="start-time">网站运行开始时间</label>
                            <input type="datetime-local" id="start-time" required>
                            <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                                用于计算网站运行时间，建议只在初次设置时修改
                            </small>
                        </div>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 12px; margin-bottom: 15px;">
                            <strong style="color: #856404;">⚠️ 注意：</strong>
                            <span style="color: #856404; font-size: 14px;">修改运行开始时间会重新计算网站运行时长，建议只在初次设置时修改。</span>
                        </div>
                        <button type="submit">更新运行时间</button>
                    </form>
                </div>

                <div class="card">
                    <h2>预览效果</h2>
                    <div style="border: 1px solid #ddd; border-radius: 4px; padding: 20px; background-color: #f9f9f9;">
                        <div id="preview-header" style="text-align: center; margin-bottom: 20px;">
                            <div id="preview-logo" style="font-size: 24px; font-weight: bold; color: #333;">我的导航网站</div>
                        </div>
                        <div id="preview-footer" style="text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
                            <div id="preview-runtime" style="margin-bottom: 5px;">网站已运行: <span style="color: #27ae60;">0天0小时0分钟</span></div>
                            <div id="preview-icp" style="display: none; margin-bottom: 3px;"></div>
                            <div id="preview-police" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 编辑链接弹窗 -->
    <div id="editLinkModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>编辑链接</h2>
                <span class="close" onclick="closeEditModal()">&times;</span>
            </div>

            <form id="edit-link-form" class="fixed-width-form">
                <div class="form-group">
                    <label for="edit-link-title">标题</label>
                    <input type="text" id="edit-link-title" required>
                </div>
                <div class="form-group">
                    <label for="edit-link-url">URL</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <input type="url" id="edit-link-url" required style="flex: 1;">
                        <button type="button" id="edit-fetch-info-btn" onclick="fetchSiteInfoForEdit()" style="padding: 8px 15px; background-color: #27ae60; color: white; border: none; border-radius: 4px; cursor: pointer; white-space: nowrap;">
                            🔍 识别
                        </button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="edit-link-description">描述</label>
                    <textarea id="edit-link-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="edit-link-icon">自定义图标URL（可选）</label>
                    <input type="url" id="edit-link-icon" placeholder="https://example.com/icon.png">
                    <small style="color: #666; font-size: 12px; display: block; margin-top: 5px;">
                        留空则使用网站默认图标，支持 .png、.jpg、.ico、.svg 格式
                    </small>
                </div>
                <div class="form-group">
                    <label for="edit-link-category">分类</label>
                    <select id="edit-link-category" required>
                        <!-- 分类选项将通过JS动态生成 -->
                    </select>
                </div>
            </form>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditModal()">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveEditLink()">保存</button>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 检查身份验证
            checkAuth();

            // 导航切换
            const navLinks = document.querySelectorAll('.nav-link');
            const sections = document.querySelectorAll('.section');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    const sectionId = this.getAttribute('data-section');

                    sections.forEach(section => {
                        section.classList.add('hidden');
                    });

                    document.getElementById(sectionId).classList.remove('hidden');
                });
            });

            // 加载统计数据
            fetchStats();

            // 初始化图表
            initChart();

            // 先加载分类数据，然后加载链接数据
            loadData();

            // 加载站点设置
            loadSiteSettings();

            // 表单提交事件
            document.getElementById('category-form').addEventListener('submit', addCategory);
            document.getElementById('link-form').addEventListener('submit', addLink);
            document.getElementById('settings-form').addEventListener('submit', saveSettings);
            document.getElementById('runtime-form').addEventListener('submit', saveRuntime);
            document.getElementById('password-form').addEventListener('submit', changePassword);

            // 图表周期选择事件
            document.getElementById('chart-period').addEventListener('change', function() {
                updateChart(this.value);
            });

            // 搜索功能事件监听
            document.getElementById('category-filter').addEventListener('change', filterLinks);
            document.getElementById('search-input').addEventListener('input', filterLinks);
        });

        // 检查身份验证
        async function checkAuth() {
            try {
                const response = await fetch('/admin/check-auth');
                if (!response.ok) {
                    // 未认证，跳转到登录页面
                    window.location.href = '/admin/login';
                    return;
                }

                const data = await response.json();
                if (data.user && data.user.username) {
                    document.getElementById('username').textContent = data.user.username;
                }
            } catch (error) {
                console.error('身份验证检查失败:', error);
                window.location.href = '/admin/login';
            }
        }

        // 登出功能
        async function logout() {
            if (!confirm('确定要登出吗？')) {
                return;
            }

            try {
                const response = await fetch('/admin/logout', {
                    method: 'POST'
                });

                if (response.ok) {
                    alert('登出成功');
                    window.location.href = '/admin/login';
                } else {
                    alert('登出失败');
                }
            } catch (error) {
                console.error('登出失败:', error);
                alert('登出失败');
            }
        }

        // 图表相关变量
        let statsChart = null;

        // 链接数据相关变量
        let allLinks = [];
        let allCategories = [];
        let categoryMap = {};

        // 初始化图表
        function initChart() {
            const ctx = document.getElementById('statsChart').getContext('2d');

            statsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [
                        {
                            label: '访问量',
                            data: [],
                            borderColor: '#3498db',
                            backgroundColor: 'rgba(52, 152, 219, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '点击量',
                            data: [],
                            borderColor: '#2ecc71',
                            backgroundColor: 'rgba(46, 204, 113, 0.1)',
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: 'IP数',
                            data: [],
                            borderColor: '#95a5a6',
                            backgroundColor: 'rgba(149, 165, 166, 0.1)',
                            tension: 0.4,
                            fill: true
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        },
                        legend: {
                            position: 'top',
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            // 初始加载7天数据
            updateChart(7);
        }

        // 更新图表数据
        async function updateChart(days) {
            try {
                const response = await fetch(`/admin/stats/chart?days=${days}`);
                const data = await response.json();

                if (response.ok) {
                    statsChart.data.labels = data.labels;
                    statsChart.data.datasets[0].data = data.visits;
                    statsChart.data.datasets[1].data = data.clicks;
                    statsChart.data.datasets[2].data = data.ips;
                    statsChart.update();
                } else {
                    console.error('获取图表数据失败:', data.message);
                }
            } catch (error) {
                console.error('获取图表数据失败:', error);
            }
        }

        // 按顺序加载数据
        async function loadData() {
            try {
                // 先加载分类数据
                await fetchCategories();
                // 然后加载链接数据
                await fetchLinks();
            } catch (error) {
                console.error('加载数据失败:', error);
            }
        }

        // 获取统计数据
        async function fetchStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                const statsContainer = document.getElementById('stats-container');
                statsContainer.innerHTML = `
                    <div class="stats-container">
                        <div class="stats-card">
                            <h3>访问量</h3>
                            <p>${data.visits}</p>
                        </div>
                        <div class="stats-card">
                            <h3>分类数</h3>
                            <p>${data.categories}</p>
                        </div>
                        <div class="stats-card">
                            <h3>链接数</h3>
                            <p>${data.links}</p>
                        </div>
                    </div>
                `;
            } catch (error) {
                console.error('获取统计数据失败:', error);
            }
        }
        
        // 获取分类数据
        async function fetchCategories() {
            try {
                const response = await fetch('/admin/categories');
                const categories = await response.json();

                // 保存分类数据到全局变量
                allCategories = categories;
                categoryMap = {};
                categories.forEach(category => {
                    categoryMap[category._id] = category.name;
                });

                // 更新分类表格
                const tbody = document.querySelector('#categories-table tbody');
                tbody.innerHTML = '';

                categories.forEach((category, index) => {
                    const tr = document.createElement('tr');
                    tr.innerHTML = `
                        <td>${category.name}</td>
                        <td>${category.order}</td>
                        <td>
                            <button onclick="moveCategoryUp('${category._id}')" ${index === 0 ? 'disabled' : ''} class="move-btn" title="上移">↑</button>
                            <button onclick="moveCategoryDown('${category._id}')" ${index === categories.length - 1 ? 'disabled' : ''} class="move-btn" title="下移">↓</button>
                            <button onclick="editCategory('${category._id}', '${category.name}', ${category.order})" class="edit-btn">编辑</button>
                            <button onclick="deleteCategory('${category._id}')" class="delete-btn">删除</button>
                        </td>
                    `;
                    tbody.appendChild(tr);
                });

                // 更新链接表单中的分类选择
                const select = document.getElementById('link-category');
                select.innerHTML = '';

                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category._id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });

                // 更新搜索筛选器中的分类选择
                const filterSelect = document.getElementById('category-filter');
                // 保留"所有分类"选项
                filterSelect.innerHTML = '<option value="">所有分类</option>';

                categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category._id;
                    option.textContent = category.name;
                    filterSelect.appendChild(option);
                });
            } catch (error) {
                console.error('获取分类数据失败:', error);
            }
        }
        
        // 获取链接数据
        async function fetchLinks() {
            try {
                const response = await fetch('/admin/links');
                const links = await response.json();

                // 保存链接数据到全局变量
                allLinks = links;

                // 渲染链接表格
                renderLinks(links);
            } catch (error) {
                console.error('获取链接数据失败:', error);
            }
        }

        // 渲染链接表格
        function renderLinks(links) {
            const tbody = document.querySelector('#links-table tbody');
            tbody.innerHTML = '';

            if (links.length === 0) {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td colspan="5" style="text-align: center; color: #666; padding: 20px;">
                        没有找到匹配的链接
                    </td>
                `;
                tbody.appendChild(tr);
                return;
            }

            links.forEach(link => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${link.title}</td>
                    <td><a href="${link.url}" target="_blank" style="color: #3498db; text-decoration: none;">${link.url}</a></td>
                    <td>${categoryMap[link.categoryId] || '未知分类'}</td>
                    <td>${link.clicks || 0}</td>
                    <td>
                        <button onclick="editLink('${link._id}')" style="margin-right: 5px; padding: 5px 10px; background-color: #3498db; color: white; border: none; border-radius: 3px; cursor: pointer;">编辑</button>
                        <button onclick="deleteLink('${link._id}')" style="padding: 5px 10px; background-color: #e74c3c; color: white; border: none; border-radius: 3px; cursor: pointer;">删除</button>
                    </td>
                `;
                tbody.appendChild(tr);
            });
        }

        // 筛选链接
        function filterLinks() {
            const categoryFilter = document.getElementById('category-filter').value;
            const searchText = document.getElementById('search-input').value.toLowerCase().trim();
            const resultsInfo = document.getElementById('search-results-info');

            let filteredLinks = allLinks;

            // 按分类筛选
            if (categoryFilter) {
                filteredLinks = filteredLinks.filter(link => link.categoryId === categoryFilter);
            }

            // 按搜索文本筛选（标题或URL）
            if (searchText) {
                filteredLinks = filteredLinks.filter(link =>
                    link.title.toLowerCase().includes(searchText) ||
                    link.url.toLowerCase().includes(searchText)
                );
            }

            // 渲染筛选后的结果
            renderLinks(filteredLinks);

            // 显示搜索结果信息
            if (categoryFilter || searchText) {
                const categoryName = categoryFilter ? categoryMap[categoryFilter] : '所有分类';
                const searchInfo = searchText ? `包含"${searchText}"` : '';
                const separator = categoryFilter && searchText ? '，' : '';

                resultsInfo.innerHTML = `
                    📊 在${categoryName}中${separator}${searchInfo} 找到 ${filteredLinks.length} 个链接
                `;
                resultsInfo.style.display = 'block';
            } else {
                resultsInfo.style.display = 'none';
            }
        }

        // 清除搜索
        function clearSearch() {
            document.getElementById('category-filter').value = '';
            document.getElementById('search-input').value = '';
            document.getElementById('search-results-info').style.display = 'none';
            renderLinks(allLinks);
        }
        
        // 添加分类
        async function addCategory(e) {
            e.preventDefault();
            
            const nameInput = document.getElementById('category-name');
            const orderInput = document.getElementById('category-order');
            
            const category = {
                name: nameInput.value,
                order: parseInt(orderInput.value) || 0
            };
            
            try {
                const response = await fetch('/admin/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(category)
                });
                
                if (response.ok) {
                    nameInput.value = '';
                    orderInput.value = '0';
                    await fetchCategories();
                    await fetchLinks(); // 重新加载链接以更新分类显示
                    fetchStats();
                    alert('分类添加成功');
                } else {
                    const error = await response.json();
                    alert('添加失败: ' + error.message);
                }
            } catch (error) {
                console.error('添加分类失败:', error);
                alert('添加分类失败');
            }
        }
        
        // 编辑分类
        async function editCategory(id, name, order) {
            const newName = prompt('输入新的分类名称:', name);
            if (newName === null) return;
            
            const newOrder = prompt('输入新的排序顺序:', order);
            if (newOrder === null) return;
            
            try {
                const response = await fetch(`/admin/categories/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: newName,
                        order: parseInt(newOrder) || 0
                    })
                });
                
                if (response.ok) {
                    await fetchCategories();
                    await fetchLinks(); // 重新加载链接以更新分类显示
                    alert('分类更新成功');
                } else {
                    const error = await response.json();
                    alert('更新失败: ' + error.message);
                }
            } catch (error) {
                console.error('更新分类失败:', error);
                alert('更新分类失败');
            }
        }
        
        // 删除分类
        async function deleteCategory(id) {
            if (!confirm('确定要删除这个分类吗？这将同时删除该分类下的所有链接！')) {
                return;
            }

            try {
                const response = await fetch(`/admin/categories/${id}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    await fetchCategories();
                    await fetchLinks(); // 重新加载链接以更新分类显示
                    fetchStats();
                    alert('分类删除成功');
                } else {
                    const error = await response.json();
                    alert('删除失败: ' + error.message);
                }
            } catch (error) {
                console.error('删除分类失败:', error);
                alert('删除分类失败');
            }
        }

        // 上移分类
        async function moveCategoryUp(id) {
            const currentIndex = allCategories.findIndex(cat => cat._id === id);
            if (currentIndex <= 0) return; // 已经是第一个或找不到

            // 交换当前分类和上一个分类的order值
            const currentCategory = allCategories[currentIndex];
            const previousCategory = allCategories[currentIndex - 1];

            const tempOrder = currentCategory.order;
            currentCategory.order = previousCategory.order;
            previousCategory.order = tempOrder;

            try {
                // 批量更新分类顺序
                await updateCategoriesOrder([currentCategory, previousCategory]);
                await fetchCategories();
                await fetchLinks(); // 重新加载链接以更新分类显示
            } catch (error) {
                console.error('上移分类失败:', error);
                alert('上移分类失败');
            }
        }

        // 下移分类
        async function moveCategoryDown(id) {
            const currentIndex = allCategories.findIndex(cat => cat._id === id);
            if (currentIndex < 0 || currentIndex >= allCategories.length - 1) return; // 已经是最后一个或找不到

            // 交换当前分类和下一个分类的order值
            const currentCategory = allCategories[currentIndex];
            const nextCategory = allCategories[currentIndex + 1];

            const tempOrder = currentCategory.order;
            currentCategory.order = nextCategory.order;
            nextCategory.order = tempOrder;

            try {
                // 批量更新分类顺序
                await updateCategoriesOrder([currentCategory, nextCategory]);
                await fetchCategories();
                await fetchLinks(); // 重新加载链接以更新分类显示
            } catch (error) {
                console.error('下移分类失败:', error);
                alert('下移分类失败');
            }
        }

        // 批量更新分类顺序
        async function updateCategoriesOrder(categories) {
            const promises = categories.map(category =>
                fetch(`/admin/categories/${category._id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: category.name,
                        order: category.order
                    })
                })
            );

            const responses = await Promise.all(promises);

            // 检查是否所有请求都成功
            for (const response of responses) {
                if (!response.ok) {
                    const error = await response.json();
                    throw new Error(error.message || '更新分类顺序失败');
                }
            }
        }
        
        // 添加链接
        async function addLink(e) {
            e.preventDefault();

            const titleInput = document.getElementById('link-title');
            const urlInput = document.getElementById('link-url');
            const descriptionInput = document.getElementById('link-description');
            const iconInput = document.getElementById('link-icon');
            const categorySelect = document.getElementById('link-category');

            const link = {
                title: titleInput.value,
                url: urlInput.value,
                description: descriptionInput.value,
                customIcon: iconInput.value.trim() || null,
                categoryId: categorySelect.value
            };
            
            try {
                const response = await fetch('/admin/links', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(link)
                });
                
                if (response.ok) {
                    titleInput.value = '';
                    urlInput.value = '';
                    descriptionInput.value = '';
                    iconInput.value = '';
                    await fetchLinks();
                    fetchStats();
                    alert('链接添加成功');
                } else {
                    const error = await response.json();
                    alert('添加失败: ' + error.message);
                }
            } catch (error) {
                console.error('添加链接失败:', error);
                alert('添加链接失败');
            }
        }
        
        // 编辑链接变量
        let currentEditLinkId = null;

        // 编辑链接
        async function editLink(id) {
            try {
                const response = await fetch(`/admin/links/${id}`);
                const link = await response.json();

                // 保存当前编辑的链接ID
                currentEditLinkId = id;

                // 填充表单数据
                document.getElementById('edit-link-title').value = link.title || '';
                document.getElementById('edit-link-url').value = link.url || '';
                document.getElementById('edit-link-description').value = link.description || '';
                document.getElementById('edit-link-icon').value = link.customIcon || '';

                // 填充分类选择
                const categorySelect = document.getElementById('edit-link-category');
                categorySelect.innerHTML = '';

                allCategories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category._id;
                    option.textContent = category.name;
                    if (category._id === link.categoryId) {
                        option.selected = true;
                    }
                    categorySelect.appendChild(option);
                });

                // 显示弹窗
                document.getElementById('editLinkModal').style.display = 'block';

            } catch (error) {
                console.error('获取链接信息失败:', error);
                alert('获取链接信息失败');
            }
        }

        // 关闭编辑弹窗
        function closeEditModal() {
            document.getElementById('editLinkModal').style.display = 'none';
            currentEditLinkId = null;
        }

        // 保存编辑的链接
        async function saveEditLink() {
            if (!currentEditLinkId) {
                alert('没有选择要编辑的链接');
                return;
            }

            const titleInput = document.getElementById('edit-link-title');
            const urlInput = document.getElementById('edit-link-url');
            const descriptionInput = document.getElementById('edit-link-description');
            const iconInput = document.getElementById('edit-link-icon');
            const categorySelect = document.getElementById('edit-link-category');

            // 验证必填字段
            if (!titleInput.value.trim()) {
                alert('请输入标题');
                titleInput.focus();
                return;
            }

            if (!urlInput.value.trim()) {
                alert('请输入URL');
                urlInput.focus();
                return;
            }

            if (!categorySelect.value) {
                alert('请选择分类');
                categorySelect.focus();
                return;
            }

            try {
                const response = await fetch(`/admin/links/${currentEditLinkId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: titleInput.value.trim(),
                        url: urlInput.value.trim(),
                        description: descriptionInput.value.trim(),
                        customIcon: iconInput.value.trim() || null,
                        categoryId: categorySelect.value
                    })
                });

                if (response.ok) {
                    await fetchLinks();
                    closeEditModal();
                    alert('链接更新成功');
                } else {
                    const error = await response.json();
                    alert('更新失败: ' + error.message);
                }
            } catch (error) {
                console.error('更新链接失败:', error);
                alert('更新链接失败');
            }
        }
        
        // 删除链接
        async function deleteLink(id) {
            if (!confirm('确定要删除这个链接吗？')) {
                return;
            }
            
            try {
                const response = await fetch(`/admin/links/${id}`, {
                    method: 'DELETE'
                });
                
                if (response.ok) {
                    await fetchLinks();
                    fetchStats();
                    alert('链接删除成功');
                } else {
                    const error = await response.json();
                    alert('删除失败: ' + error.message);
                }
            } catch (error) {
                console.error('删除链接失败:', error);
                alert('删除链接失败');
            }
        }

        // 获取网站信息
        async function fetchSiteInfo() {
            const urlInput = document.getElementById('link-url');
            const titleInput = document.getElementById('link-title');
            const descriptionInput = document.getElementById('link-description');
            const fetchBtn = document.getElementById('fetch-info-btn');

            const url = urlInput.value.trim();
            if (!url) {
                alert('请先输入URL');
                return;
            }

            // 禁用按钮并显示加载状态
            fetchBtn.disabled = true;
            fetchBtn.innerHTML = '⏳ 识别中...';
            fetchBtn.style.backgroundColor = '#95a5a6';

            try {
                const response = await fetch('/admin/fetch-site-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url: url })
                });

                const data = await response.json();

                if (response.ok) {
                    // 自动填入标题和描述（每次识别都更新）
                    if (data.title) {
                        titleInput.value = data.title;
                        // 添加更新动画效果
                        titleInput.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            titleInput.style.backgroundColor = '';
                        }, 1000);
                    }

                    if (data.description) {
                        descriptionInput.value = data.description;
                        // 添加更新动画效果
                        descriptionInput.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            descriptionInput.style.backgroundColor = '';
                        }, 1000);
                    }

                    // 显示成功提示
                    const successMsg = document.createElement('div');
                    successMsg.style.cssText = 'color: #27ae60; font-size: 12px; margin-top: 5px;';

                    // 根据获取到的内容显示不同的提示
                    let message = '✅ ';
                    if (data.title && data.description) {
                        message += '标题和描述已更新！';
                    } else if (data.title) {
                        message += '标题已更新！';
                    } else if (data.description) {
                        message += '描述已更新！';
                    } else {
                        message += '网站信息获取成功！';
                    }
                    successMsg.textContent = message;

                    // 移除之前的提示
                    const existingMsg = urlInput.parentNode.querySelector('.fetch-message');
                    if (existingMsg) {
                        existingMsg.remove();
                    }

                    successMsg.className = 'fetch-message';
                    urlInput.parentNode.appendChild(successMsg);

                    // 3秒后移除提示
                    setTimeout(() => {
                        if (successMsg.parentNode) {
                            successMsg.remove();
                        }
                    }, 3000);

                } else {
                    // 显示错误信息
                    alert('获取失败: ' + data.message);
                }

            } catch (error) {
                console.error('获取网站信息失败:', error);
                alert('获取网站信息失败，请检查网络连接');
            } finally {
                // 恢复按钮状态
                fetchBtn.disabled = false;
                fetchBtn.innerHTML = '🔍 识别';
                fetchBtn.style.backgroundColor = '#27ae60';
            }
        }

        // 编辑弹窗的获取网站信息
        async function fetchSiteInfoForEdit() {
            const urlInput = document.getElementById('edit-link-url');
            const titleInput = document.getElementById('edit-link-title');
            const descriptionInput = document.getElementById('edit-link-description');
            const fetchBtn = document.getElementById('edit-fetch-info-btn');

            const url = urlInput.value.trim();
            if (!url) {
                alert('请先输入URL');
                return;
            }

            // 禁用按钮并显示加载状态
            fetchBtn.disabled = true;
            fetchBtn.innerHTML = '⏳ 识别中...';
            fetchBtn.style.backgroundColor = '#95a5a6';

            try {
                const response = await fetch('/admin/fetch-site-info', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ url: url })
                });

                const data = await response.json();

                if (response.ok) {
                    // 自动填入标题和描述（每次识别都更新）
                    if (data.title) {
                        titleInput.value = data.title;
                        // 添加更新动画效果
                        titleInput.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            titleInput.style.backgroundColor = '';
                        }, 1000);
                    }

                    if (data.description) {
                        descriptionInput.value = data.description;
                        // 添加更新动画效果
                        descriptionInput.style.backgroundColor = '#d4edda';
                        setTimeout(() => {
                            descriptionInput.style.backgroundColor = '';
                        }, 1000);
                    }

                    // 显示成功提示
                    const successMsg = document.createElement('div');
                    successMsg.style.cssText = 'color: #27ae60; font-size: 12px; margin-top: 5px;';

                    // 根据获取到的内容显示不同的提示
                    let message = '✅ ';
                    if (data.title && data.description) {
                        message += '标题和描述已更新！';
                    } else if (data.title) {
                        message += '标题已更新！';
                    } else if (data.description) {
                        message += '描述已更新！';
                    } else {
                        message += '网站信息获取成功！';
                    }
                    successMsg.textContent = message;

                    // 移除之前的提示
                    const existingMsg = urlInput.parentNode.querySelector('.fetch-message');
                    if (existingMsg) {
                        existingMsg.remove();
                    }

                    successMsg.className = 'fetch-message';
                    urlInput.parentNode.appendChild(successMsg);

                    // 3秒后移除提示
                    setTimeout(() => {
                        if (successMsg.parentNode) {
                            successMsg.remove();
                        }
                    }, 3000);

                } else {
                    // 显示错误信息
                    alert('获取失败: ' + data.message);
                }

            } catch (error) {
                console.error('获取网站信息失败:', error);
                alert('获取网站信息失败，请检查网络连接');
            } finally {
                // 恢复按钮状态
                fetchBtn.disabled = false;
                fetchBtn.innerHTML = '🔍 识别';
                fetchBtn.style.backgroundColor = '#27ae60';
            }
        }

        // 点击弹窗外部关闭弹窗
        window.onclick = function(event) {
            const modal = document.getElementById('editLinkModal');
            if (event.target === modal) {
                closeEditModal();
            }
        }

        // 站点设置相关函数
        let currentSettings = {};

        // 加载站点设置
        async function loadSiteSettings() {
            try {
                const response = await fetch('/admin/site-settings');
                const settings = await response.json();

                currentSettings = settings;

                // 填充表单
                document.getElementById('site-name').value = settings.siteName || '';
                document.getElementById('site-logo').value = settings.logo || '';
                document.getElementById('icp-number').value = settings.icpNumber || '';
                document.getElementById('police-number').value = settings.policeNumber || '';

                // 处理时间格式
                if (settings.startTime) {
                    const date = new Date(settings.startTime);
                    const localDateTime = new Date(date.getTime() - date.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
                    document.getElementById('start-time').value = localDateTime;
                }

                // 更新预览
                updatePreview();

                // 添加实时预览事件监听
                addPreviewListeners();

            } catch (error) {
                console.error('加载站点设置失败:', error);
            }
        }

        // 保存基本设置（不包含运行时间）
        async function saveSettings(e) {
            e.preventDefault();

            const siteName = document.getElementById('site-name').value.trim();
            const logo = document.getElementById('site-logo').value.trim();
            const icpNumber = document.getElementById('icp-number').value.trim();
            const policeNumber = document.getElementById('police-number').value.trim();

            if (!siteName) {
                alert('请输入网站名称');
                return;
            }

            try {
                const response = await fetch('/admin/site-settings', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        siteName,
                        logo,
                        icpNumber,
                        policeNumber
                        // 注意：不包含startTime
                    })
                });

                if (response.ok) {
                    const settings = await response.json();
                    currentSettings = settings;
                    updatePreview();
                    alert('基本设置保存成功');
                } else {
                    const error = await response.json();
                    alert('保存失败: ' + error.message);
                }
            } catch (error) {
                console.error('保存设置失败:', error);
                alert('保存设置失败');
            }
        }

        // 保存运行时间设置
        async function saveRuntime(e) {
            e.preventDefault();

            const startTime = document.getElementById('start-time').value;

            if (!startTime) {
                alert('请选择网站运行开始时间');
                return;
            }

            // 确认修改运行时间
            if (!confirm('确定要修改网站运行开始时间吗？这将重新计算网站运行时长。')) {
                return;
            }

            try {
                const response = await fetch('/admin/site-settings/runtime', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        startTime
                    })
                });

                if (response.ok) {
                    const settings = await response.json();
                    currentSettings = settings;
                    updatePreview();
                    alert('运行时间设置保存成功');
                } else {
                    const error = await response.json();
                    alert('保存失败: ' + error.message);
                }
            } catch (error) {
                console.error('保存运行时间失败:', error);
                alert('保存运行时间失败');
            }
        }

        // 更新预览
        function updatePreview() {
            const siteName = document.getElementById('site-name').value.trim() || '我的导航网站';
            const logo = document.getElementById('site-logo').value.trim();
            const icpNumber = document.getElementById('icp-number').value.trim();
            const policeNumber = document.getElementById('police-number').value.trim();
            const startTime = document.getElementById('start-time').value;

            // 更新logo预览
            const previewLogo = document.getElementById('preview-logo');
            if (logo) {
                previewLogo.innerHTML = `<img src="${logo}" alt="${siteName}" style="max-height: 40px; max-width: 200px;" onerror="this.style.display='none'; this.nextSibling.style.display='inline';">
                                        <span style="display: none;">${siteName}</span>`;
            } else {
                previewLogo.textContent = siteName;
            }

            // 更新运行时间预览
            if (startTime) {
                const start = new Date(startTime);
                const now = new Date();
                const diff = now - start;

                const days = Math.floor(diff / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

                document.getElementById('preview-runtime').innerHTML =
                    `网站已运行: <span style="color: #27ae60;">${days}天${hours}小时${minutes}分钟</span>`;
            }

            // 更新备案信息预览
            const previewIcp = document.getElementById('preview-icp');
            const previewPolice = document.getElementById('preview-police');

            if (icpNumber) {
                previewIcp.textContent = icpNumber;
                previewIcp.style.display = 'block';
            } else {
                previewIcp.style.display = 'none';
            }

            if (policeNumber) {
                previewPolice.textContent = policeNumber;
                previewPolice.style.display = 'block';
            } else {
                previewPolice.style.display = 'none';
            }
        }

        // 添加实时预览事件监听
        function addPreviewListeners() {
            const inputs = ['site-name', 'site-logo', 'icp-number', 'police-number', 'start-time'];
            inputs.forEach(id => {
                const element = document.getElementById(id);
                element.addEventListener('input', updatePreview);
                element.addEventListener('change', updatePreview);
            });
        }

        // 修改密码功能
        async function changePassword(e) {
            e.preventDefault();

            const currentPassword = document.getElementById('current-password').value.trim();
            const newPassword = document.getElementById('new-password').value.trim();
            const confirmPassword = document.getElementById('confirm-password').value.trim();

            // 验证输入
            if (!currentPassword) {
                alert('请输入当前密码');
                return;
            }

            if (!newPassword) {
                alert('请输入新密码');
                return;
            }

            if (newPassword.length < 6) {
                alert('新密码长度至少6位');
                return;
            }

            if (newPassword !== confirmPassword) {
                alert('两次输入的新密码不一致');
                return;
            }

            if (currentPassword === newPassword) {
                alert('新密码不能与当前密码相同');
                return;
            }

            try {
                const response = await fetch('/admin/change-password', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        currentPassword: currentPassword,
                        newPassword: newPassword
                    })
                });

                const data = await response.json();

                if (response.ok) {
                    alert('密码修改成功，请重新登录');
                    // 清空表单
                    document.getElementById('current-password').value = '';
                    document.getElementById('new-password').value = '';
                    document.getElementById('confirm-password').value = '';
                    // 跳转到登录页面
                    window.location.href = '/admin/login';
                } else {
                    alert('密码修改失败: ' + data.message);
                }
            } catch (error) {
                console.error('修改密码失败:', error);
                alert('修改密码失败，请检查网络连接');
            }
        }
    </script>
</body>
</html>
